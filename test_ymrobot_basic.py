#!/usr/bin/env python3
"""
Basic test script for YMrobot integration with LeRobot framework.

This script tests basic functionality without requiring ROS2 to be running.

Usage:
    python test_ymrobot_basic.py
"""

import sys
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from lerobot.robots.ymrobot import YMrobotConfig
from lerobot.cameras import CameraConfig


def test_ymrobot_config():
    """Test YMrobot configuration creation."""
    print("Testing YMrobot configuration...")
    
    # Test basic configuration
    config = YMrobotConfig(
        id="test_ymrobot",
        cameras={
            "head_camera": CameraConfig(width=640, height=480, fps=30),
            "left_wrist_camera": CameraConfig(width=640, height=480, fps=30),
            "right_wrist_camera": CameraConfig(width=640, height=480, fps=30),
        }
    )
    
    assert config.type == "ymrobot"
    assert config.id == "test_ymrobot"
    assert len(config.cameras) == 3
    assert config.control_frequency == 50.0
    assert len(config.left_arm_joint_names) == 7
    assert len(config.right_arm_joint_names) == 7
    
    # Test default camera configuration
    default_config = YMrobotConfig(id="default_test")
    assert len(default_config.cameras) == 3
    assert "head_camera" in default_config.cameras
    assert "left_wrist_camera" in default_config.cameras
    assert "right_wrist_camera" in default_config.cameras
    
    print("✓ YMrobot configuration test passed")
    return config


def test_robot_registration():
    """Test that YMrobot is properly registered in LeRobot framework."""
    print("Testing robot registration...")
    
    try:
        from lerobot.robots.utils import make_robot_from_config
        from lerobot import available_robots
        
        # Check if ymrobot is in available robots list
        assert "ymrobot" in available_robots, "YMrobot not found in available_robots list"
        
        # Test configuration registration
        config = YMrobotConfig(id="test_registration")
        assert config.type == "ymrobot"
        
        print("✓ Robot registration test passed")
        return True
        
    except Exception as e:
        print(f"✗ Robot registration test failed: {e}")
        return False


def test_config_validation():
    """Test configuration validation."""
    print("Testing configuration validation...")
    
    try:
        # Test valid configuration
        config = YMrobotConfig(
            id="validation_test",
            max_joint_change_deg=45.0,
            control_frequency=100.0,
            cameras={
                "head_camera": CameraConfig(width=1280, height=720, fps=60),
            }
        )
        
        assert config.max_joint_change_deg == 45.0
        assert config.control_frequency == 100.0
        assert config.cameras["head_camera"].width == 1280
        
        # Test joint names
        assert len(config.left_arm_joint_names) == 7
        assert len(config.right_arm_joint_names) == 7
        assert config.left_arm_joint_names[0] == "Left_Arm_Joint1"
        assert config.right_arm_joint_names[0] == "Right_Arm_Joint1"
        
        # Test ROS2 topics
        assert config.left_arm_topic == "/left_arm_position_controller/commands"
        assert config.right_arm_topic == "/right_arm_position_controller/commands"
        assert config.joint_state_topic == "/joint_states"
        
        print("✓ Configuration validation test passed")
        return True
        
    except Exception as e:
        print(f"✗ Configuration validation test failed: {e}")
        return False


def test_import_structure():
    """Test import structure and module organization."""
    print("Testing import structure...")
    
    try:
        # Test direct imports
        from lerobot.robots.ymrobot import YMrobot, YMrobotConfig
        from lerobot.robots.ymrobot.config_ymrobot import YMrobotConfig as DirectConfig
        from lerobot.robots.ymrobot.ymrobot import YMrobot as DirectRobot
        
        # Test that classes are the same
        assert YMrobotConfig is DirectConfig
        assert YMrobot is DirectRobot
        
        # Test robot class attributes
        assert YMrobot.name == "ymrobot"
        assert YMrobot.config_class == YMrobotConfig
        
        print("✓ Import structure test passed")
        return True
        
    except Exception as e:
        print(f"✗ Import structure test failed: {e}")
        return False


def test_framework_integration():
    """Test integration with LeRobot framework components."""
    print("Testing framework integration...")
    
    try:
        # Test that YMrobot appears in framework imports
        from lerobot.teleoperate import ymrobot as teleop_ymrobot
        from lerobot.record import ymrobot as record_ymrobot
        from lerobot.replay import ymrobot as replay_ymrobot
        
        # These should all reference the same module
        assert teleop_ymrobot is record_ymrobot
        assert record_ymrobot is replay_ymrobot
        
        print("✓ Framework integration test passed")
        return True
        
    except Exception as e:
        print(f"✗ Framework integration test failed: {e}")
        return False


def main():
    """Run all basic tests."""
    print("=" * 60)
    print("YMrobot Basic Integration Test Suite")
    print("=" * 60)
    
    tests = [
        test_ymrobot_config,
        test_robot_registration,
        test_config_validation,
        test_import_structure,
        test_framework_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            result = test_func()
            if result is not False:
                passed += 1
        except Exception as e:
            print(f"✗ {test_func.__name__} failed with exception: {e}")
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All basic tests passed! YMrobot integration successful.")
        print("\nNext steps:")
        print("1. Start your YMrobot ROS2 system")
        print("2. Test with actual robot hardware")
        print("3. Use YMrobot in LeRobot record/replay/teleoperate commands")
    else:
        print("✗ Some tests failed. Please check the errors above.")
    
    print("=" * 60)
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
