import os
import time
import argparse
import subprocess
from typing import List
import threading
import cv2

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy
from sensor_msgs.msg import Image, JointState
from std_msgs.msg import Float64MultiArray
from cv_bridge import CvBridge
import numpy as np

import dataclasses
from typing import Literal
from pathlib import Path
import shutil
import torch
from lerobot.constants import HF_LEROBOT_HOME
from lerobot.datasets.lerobot_dataset import LeRobotDataset

@dataclasses.dataclass(frozen=True)
class DatasetConfig:
    use_videos: bool = True
    tolerance_s: float = 0.0001
    image_writer_processes: int = 10
    image_writer_threads: int = 5
    video_backend: str | None = None


DEFAULT_DATASET_CONFIG = DatasetConfig()

def create_empty_dataset(
    repo_id: str,
    robot_type: str,
    mode: Literal["video", "image"] = "video",
    *,
    has_velocity: bool = False,
    has_effort: bool = False,
    dataset_config: DatasetConfig = DEFAULT_DATASET_CONFIG,
) -> LeRobotDataset:
    motors = [
        "left_arm_joint1", 
        "left_arm_joint2", 
        "left_arm_joint3", 
        "left_arm_joint4",
        "left_arm_joint5", 
        "left_arm_joint6", 
        "left_arm_joint7",
        "right_arm_joint1", 
        "right_arm_joint2", 
        "right_arm_joint3", 
        "right_arm_joint4",
        "right_arm_joint5",
        "right_arm_joint6",
        "right_arm_joint7",
        "left_gripper", 
        "right_gripper",
    ]

    cameras = [
        "head_camera",
        "left_wrist_camera",
        "right_wrist_camera",
    ]

    features = {
        "observation.state": {
            "dtype": "float32",
            "shape": (len(motors), ),
            "names": [
                motors,
            ],
        },
        "action": {
            "dtype": "float32",
            "shape": (len(motors), ),
            "names": [
                motors,
            ],
        },
    }

    if has_velocity:
        features["observation.velocity"] = {
            "dtype": "float32",
            "shape": (len(motors), ),
            "names": [
                motors,
            ],
        }

    if has_effort:
        features["observation.effort"] = {
            "dtype": "float32",
            "shape": (len(motors), ),
            "names": [
                motors,
            ],
        }

    for cam in cameras:
        features[f"observation.images.{cam}"] = {
            "dtype": mode,
            "shape": (3, 480, 640),
            "names": [
                "channels",
                "height",
                "width",
            ],
        }

    if Path(HF_LEROBOT_HOME / repo_id).exists():
        shutil.rmtree(HF_LEROBOT_HOME / repo_id)

    return LeRobotDataset.create(
        repo_id=repo_id,
        fps=50,
        robot_type=robot_type,
        features=features,
        use_videos=dataset_config.use_videos,
        tolerance_s=dataset_config.tolerance_s,
        image_writer_processes=dataset_config.image_writer_processes,
        image_writer_threads=dataset_config.image_writer_threads,
        video_backend=dataset_config.video_backend,
    )

JOINT_ORDER = [
    'Left_Arm_Joint1', 'Left_Arm_Joint2', 'Left_Arm_Joint3', 'Left_Arm_Joint4',
    'Left_Arm_Joint5', 'Left_Arm_Joint6', 'Left_Arm_Joint7',
    'Right_Arm_Joint1', 'Right_Arm_Joint2', 'Right_Arm_Joint3', 'Right_Arm_Joint4',
    'Right_Arm_Joint5', 'Right_Arm_Joint6', 'Right_Arm_Joint7',
]

class YmrobotToLerobot(Node):
    def __init__(self, repo_id: str = "ymrobot_dataset", robot_type: str = "ymrobot"): 
        super().__init__('ymrobot_to_lerobot')

        self._init_episode_buffer()

        # === 数据集相关变量 ===
        self.repo_id = repo_id
        self.robot_type = robot_type
        self.episode_count = 0

        # === 辅助变量 ===
        self.bridge = CvBridge()
        
        # === QoS: 相机 BEST_EFFORT，其它 RELIABLE ===

        reliable_qos = QoSProfile(depth=20)
        reliable_qos.reliability = ReliabilityPolicy.RELIABLE
        reliable_qos.history = HistoryPolicy.KEEP_LAST

        # === 订阅者 ===
        # 图像
        self.create_subscription(Image, '/camera/color/image_raw', self.on_head_img, 10)
        self.create_subscription(Image, '/camera1/camera1/color/image_raw', self.on_left_img, 10)
        self.create_subscription(Image, '/camera2/camera2/color/image_raw', self.on_right_img, 10)
        # 状态
        self.create_subscription(JointState, '/joint_states', self.on_joint, reliable_qos)
        self.create_subscription(Float64MultiArray, '/gripper_states', self.on_gripper_state, reliable_qos)
        # 动作
        self.create_subscription(Float64MultiArray, '/left_arm_position_controller/commands', self.on_left_action, reliable_qos)
        self.create_subscription(Float64MultiArray, '/right_arm_position_controller/commands', self.on_right_action, reliable_qos)
        self.create_subscription(Float64MultiArray, '/gripper_commands', self.on_gripper_action, reliable_qos)

        self.get_logger().info('MinimalEpisodeNode started.')

    def on_left_img(self, msg: Image):
        try:
            self.left_img = self.bridge.imgmsg_to_cv2(msg, desired_encoding='bgr8')
        except Exception as e:
            self.get_logger().warn(f'left img convert failed: {e}')

    def on_right_img(self, msg: Image):
        try:
            self.right_img = self.bridge.imgmsg_to_cv2(msg, desired_encoding='bgr8')
        except Exception as e:
            self.get_logger().warn(f'right img convert failed: {e}')

    def on_joint(self, msg: JointState):
        self.joint_state = msg

    def on_gripper_state(self, msg: Float64MultiArray):
        self.gripper_state = np.asarray(msg.data, dtype=np.float32)  # (2,)

    def on_left_action(self, msg: Float64MultiArray):
        self.left_action = np.asarray(msg.data, dtype=np.float32)    # (7,)

    def on_right_action(self, msg: Float64MultiArray):
        self.right_action = np.asarray(msg.data, dtype=np.float32)   # (7,)

    def on_gripper_action(self, msg: Float64MultiArray):
        self.gripper_action = np.asarray(msg.data, dtype=np.float32) # (2,)

    def pad_image(self, img):
        # pad height from HxW to 480x640, center vertically
        h, w = img.shape[:2]
        target_h, target_w = 480, 640
        top = (target_h - h) // 2
        bottom = target_h - h - top
        left = (target_w - w) // 2
        right = target_w - w - left
        # pad with black
        return cv2.copyMakeBorder(img, top, bottom, left, right,
                                  cv2.BORDER_CONSTANT, value=[0,0,0])
    
    def on_head_img(self, msg: Image):
        try:
            self.head_img = self.bridge.imgmsg_to_cv2(msg, desired_encoding='bgr8')
            self.head_img = self.pad_image(self.head_img)
        except Exception as e:
            self.get_logger().warn(f'head img convert failed: {e}')
            return

        # 所有必需字段都不为 None 才追加
        if not self._ready():
            return

        # 组装 observation.state
        qpos = self._extract_qpos(self.joint_state, JOINT_ORDER)
        if qpos is None:
            # joint_state 不完整
            return

        # 长度基本校验
        if (self.left_action is None or self.left_action.size != 7 or
            self.right_action is None or self.right_action.size != 7 or
            self.gripper_state is None or self.gripper_state.size != 2 or
            self.gripper_action is None or self.gripper_action.size != 2):
            # 如需强制追加，去掉这些判断
            return

        state_vec = np.concatenate([
            qpos.astype(np.float32),
            self.gripper_state.astype(np.float32)
        ]).tolist()

        # action: [7*left, 7*right, 2*gripper] -> 16
        action_vec = np.concatenate([
            self.left_action.astype(np.float32),
            self.right_action.astype(np.float32),
            self.gripper_action.astype(np.float32)
        ]).tolist()

        # 追加
        self.episode["observation.state"].append(state_vec)
        self.episode["action"].append(action_vec)
        self.episode["observation.images.head_camera"].append(self.head_img.copy())
        self.episode["observation.images.left_wrist_camera"].append(self.left_img.copy())
        self.episode["observation.images.right_wrist_camera"].append(self.right_img.copy())

        self.sample_count += 1
        if self.sample_count % 50 == 0:
            h, w, _ = self.head_img.shape
            self.get_logger().info(f'[{self.sample_count}] appended | head={w}x{h} | episode_len={self.sample_count}')

    # ------- 小工具 -------
    def _ready(self) -> bool:
        return (self.head_img is not None and
                self.left_img is not None and
                self.right_img is not None and
                self.joint_state is not None and
                self.gripper_state is not None and
                self.left_action is not None and
                self.right_action is not None and
                self.gripper_action is not None)

    @staticmethod
    def _extract_qpos(joint_msg: JointState, order):
        if joint_msg is None:
            return None
        name_to_idx = {n: i for i, n in enumerate(joint_msg.name)}
        qpos = np.full((len(order),), np.nan, dtype=np.float32)
        filled = True
        for i, name in enumerate(order):
            idx = name_to_idx.get(name, None)
            if idx is None or idx >= len(joint_msg.position):
                filled = False
                break
            qpos[i] = float(joint_msg.position[idx])
        return qpos if filled else None
        
        
    def _init_episode_buffer(self):
        self.episode = {
            "observation.state": [],
            "action": [],
            "observation.images.head_camera": [],
            "observation.images.left_wrist_camera": [],
            "observation.images.right_wrist_camera": []
        }

        self.head_img = None
        self.left_img = None
        self.right_img = None
        self.joint_state = None
        self.gripper_state = None
        self.left_action = None
        self.right_action = None
        self.gripper_action = None

        self.sample_count = 0

        self.dataset = None

    def save_data(self):
        """将self.episode中的数据保存为LeRobotDataset格式"""
        if self.sample_count == 0:
            self.get_logger().warn("No data to save, episode is empty")
            return

        try:
            # 创建数据集（如果还没有创建）
            if self.dataset is None:
                self.get_logger().info(f"Creating new dataset: {self.repo_id}")
                self.dataset = create_empty_dataset(
                    repo_id=self.repo_id,
                    robot_type=self.robot_type,
                    mode="image",  # 使用image模式
                    dataset_config=DEFAULT_DATASET_CONFIG,
                )

            # 获取episode长度并验证数据一致性
            num_frames = len(self.episode["observation.state"])

            if num_frames == 0:
                self.get_logger().warn("No frames to save")
                return

            # 验证所有数据列表长度一致
            expected_keys = ["observation.state", "action", "observation.images.head_camera",
                           "observation.images.left_wrist_camera", "observation.images.right_wrist_camera"]
            for key in expected_keys:
                if len(self.episode[key]) != num_frames:
                    self.get_logger().error(f"Data inconsistency: {key} has {len(self.episode[key])} frames, expected {num_frames}")
                    return

            self.get_logger().info(f"Saving episode {self.episode_count} with {num_frames} frames")

            # 为每一帧添加数据
            for i in range(num_frames):
                try:
                    # 验证状态和动作数据
                    state_data = self.episode["observation.state"][i]
                    action_data = self.episode["action"][i]

                    if len(state_data) != 16:
                        self.get_logger().error(f"Frame {i}: state has {len(state_data)} elements, expected 16")
                        continue

                    if len(action_data) != 16:
                        self.get_logger().error(f"Frame {i}: action has {len(action_data)} elements, expected 16")
                        continue

                    # 转换图像格式：从BGR(OpenCV)转换为RGB，并调整维度顺序为(C,H,W)
                    head_img = self.episode["observation.images.head_camera"][i]
                    left_img = self.episode["observation.images.left_wrist_camera"][i]
                    right_img = self.episode["observation.images.right_wrist_camera"][i]

                    # 验证图像尺寸
                    for img_name, img in [("head", head_img), ("left", left_img), ("right", right_img)]:
                        if img.shape != (480, 640, 3):
                            self.get_logger().warn(f"Frame {i}: {img_name} image shape {img.shape}, expected (480, 640, 3)")

                    # BGR -> RGB 并转换为 (C,H,W) 格式
                    head_img_rgb = head_img[:, :, ::-1].transpose(2, 0, 1)  # BGR->RGB, HWC->CHW
                    left_img_rgb = left_img[:, :, ::-1].transpose(2, 0, 1)
                    right_img_rgb = right_img[:, :, ::-1].transpose(2, 0, 1)

                    frame = {
                        "observation.state": torch.tensor(state_data, dtype=torch.float32),
                        "action": torch.tensor(action_data, dtype=torch.float32),
                        "observation.images.head_camera": head_img_rgb,
                        "observation.images.left_wrist_camera": left_img_rgb,
                        "observation.images.right_wrist_camera": right_img_rgb,
                    }

                    task = "right arm turn on the tap and left arm pour water"

                    self.dataset.add_frame(frame, task=task)

                except Exception as e:
                    self.get_logger().error(f"Error processing frame {i}: {e}")
                    continue

            # 保存episode
            self.dataset.save_episode()
            self.episode_count += 1

            self.get_logger().info(f"Successfully saved episode {self.episode_count - 1} to dataset {self.repo_id}")

        except Exception as e:
            self.get_logger().error(f"Error saving episode: {e}")
            import traceback
            self.get_logger().error(f"Traceback: {traceback.format_exc()}")

def find_bag_directories(root: str) -> List[str]:
    """返回 root 下所有 rosbag2 目录（包含 metadata.yaml）。若 root 本身是 bag，也会被返回。"""
    def is_bag_dir(p: str) -> bool:
        return os.path.isdir(p) and os.path.exists(os.path.join(p, 'metadata.yaml'))
    bags = []
    if is_bag_dir(root):
        bags.append(root)
    else:
        for name in sorted(os.listdir(root)):
            p = os.path.join(root, name)
            if is_bag_dir(p):
                bags.append(p)
    return bags

def play_one_bag(bag_dir: str, extra_args: List[str] = None) -> int:
    """阻塞直至播放完成，返回进程退出码。"""
    cmd = ['ros2', 'bag', 'play', bag_dir]
    if extra_args:
        cmd.extend(extra_args)
    print(f'[player] Exec: {" ".join(cmd)}')
    # 提前给订阅者一点时间（也可用 --start-paused 更严谨）
    time.sleep(0.5)
    return subprocess.run(cmd).returncode


def main():
    parser = argparse.ArgumentParser(description='Sequentially play ros2 bags and collect episodes.')
    parser.add_argument('root', type=str, help='包含多个 rosbag2 目录的路径；或单个 rosbag2 目录')
    parser.add_argument('--play-args', type=str, nargs=argparse.REMAINDER,
                        help='传给 ros2 bag play 的其它参数（例如 --rate 0.5 --clock）')
    parser.add_argument('--repo-id', type=str, default='ymrobot_dataset',
                        help='LeRobot数据集的repo_id')
    parser.add_argument('--robot-type', type=str, default='ymrobot',
                        help='机器人类型')
    args = parser.parse_args()

    bag_dirs = find_bag_directories(args.root)
    if not bag_dirs:
        print(f'No rosbag2 directories found under: {args.root}')
        return

    rclpy.init()
    node = YmrobotToLerobot(repo_id=args.repo_id, robot_type=args.robot_type)

    try:
         # 用独立线程执行器让订阅节点一直 spin
        executor = rclpy.executors.MultiThreadedExecutor()
        executor.add_node(node)

        import threading
        spin_thread = threading.Thread(target=executor.spin, daemon=True)
        spin_thread.start()

        for bag in bag_dirs:
            print(f'\n=== Playing: {bag} ===')
            rc = play_one_bag(bag, args.play_args)
            if rc != 0:
                print(f'[player] bag exited with code {rc} (skipping save)')
                node._init_episode_buffer()
                continue

            # 播放结束，稍等尾帧
            time.sleep(0.2)
            # 存一份本条 episode
            node.save_data()
            # 最后一行：清空 episode，准备下一条
            node._init_episode_buffer()

        # 播完了
        executor.shutdown()
        node.destroy_node()
    finally:
        rclpy.shutdown()


if __name__ == '__main__':
    main()