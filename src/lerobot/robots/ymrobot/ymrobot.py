#!/usr/bin/env python

# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import logging
import time
import threading
from functools import cached_property
from typing import Any

import numpy as np

# Optional ROS2 imports
try:
    import rclpy
    from rclpy.node import Node
    from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy, qos_profile_sensor_data
    from sensor_msgs.msg import JointState, Image
    from std_msgs.msg import Float64MultiArray
    from cv_bridge import CvBridge
    import cv2
    ROS2_AVAILABLE = True
except ImportError as e:
    ROS2_AVAILABLE = False
    _ros2_import_error = e
    # Create dummy classes for type hints
    Node = object
    QoSProfile = object
    ReliabilityPolicy = object
    HistoryPolicy = object
    qos_profile_sensor_data = None
    JointState = object
    Image = object
    Float64MultiArray = object
    CvBridge = object
    cv2 = None

from lerobot.cameras.utils import Camera
from lerobot.common.utils.utils import init_logging
from lerobot.robots.robot import Robot
from lerobot.errors import DeviceNotConnectedError

from .config_ymrobot import YMrobotConfig

logger = logging.getLogger(__name__)


class YMrobotCamera(Camera):
    """Camera interface for YMrobot ROS2 image topics."""
    
    def __init__(self, topic: str, width: int, height: int, fps: int):
        self.topic = topic
        self.width = width
        self.height = height
        self.fps = fps
        self.bridge = CvBridge() if ROS2_AVAILABLE else None
        self.latest_image = None
        self.image_timestamp = None
        self._is_connected = False
        
    def connect(self):
        """Connect to the camera (handled by YMrobot node)."""
        self._is_connected = True
        
    def disconnect(self):
        """Disconnect from the camera."""
        self._is_connected = False
        
    @property
    def is_connected(self) -> bool:
        return self._is_connected
        
    def async_read(self) -> np.ndarray | None:
        """Return the latest image."""
        if self.latest_image is None:
            return None
        return self.latest_image.copy()
        
    def update_image(self, image_msg):
        """Update the latest image from ROS2 message."""
        if not ROS2_AVAILABLE:
            logger.error("Cannot update image: ROS2 not available")
            return

        try:
            # Convert ROS2 image to OpenCV format (BGR)
            cv_image = self.bridge.imgmsg_to_cv2(image_msg, 'bgr8')

            # Pad image to target size if needed
            if cv_image.shape[:2] != (self.height, self.width):
                cv_image = self._pad_image(cv_image)

            # Convert BGR to RGB for LeRobot
            self.latest_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
            self.image_timestamp = image_msg.header.stamp
        except Exception as e:
            logger.warning(f"Failed to convert image from topic {self.topic}: {e}")

    def _pad_image(self, img):
        """Pad image to target size, center it."""
        if cv2 is None:
            logger.error("Cannot pad image: OpenCV not available")
            return img

        h, w = img.shape[:2]
        target_h, target_w = self.height, self.width
        top = (target_h - h) // 2
        bottom = target_h - h - top
        left = (target_w - w) // 2
        right = target_w - w - left
        return cv2.copyMakeBorder(img, top, bottom, left, right,
                                  cv2.BORDER_CONSTANT, value=[0, 0, 0])


class YMrobot(Robot, Node if ROS2_AVAILABLE else object):
    """
    YMrobot dual-arm robot with ROS2 interface for LeRobot framework.
    
    This robot features:
    - Dual 7-DOF arms (left and right)
    - Dual grippers with RS485 control
    - Multiple cameras (head, left wrist, right wrist)
    - ROS2 communication interface
    """
    
    config_class = YMrobotConfig
    name = "ymrobot"
    
    def __init__(self, config: YMrobotConfig):
        # Check ROS2 availability
        if not ROS2_AVAILABLE:
            raise ImportError(
                f"ROS2 is not available for YMrobot. "
                f"Original error: {_ros2_import_error}. "
                f"Please ensure ROS2 is properly installed and sourced."
            )

        # Initialize Robot base class
        Robot.__init__(self, config)

        # Initialize ROS2 node
        if not rclpy.ok():
            rclpy.init()
        Node.__init__(self, config.ros2_node_name)
        
        self.config = config
        self._is_connected = False
        self._is_calibrated = True  # YMrobot doesn't require calibration
        
        # Joint state storage
        self.joint_state = None
        self.gripper_state = None
        
        # Initialize cameras
        self.cameras = {}
        for cam_name, cam_config in config.cameras.items():
            topic = getattr(config, f"{cam_name}_topic")
            self.cameras[cam_name] = YMrobotCamera(
                topic=topic,
                width=cam_config.width,
                height=cam_config.height,
                fps=cam_config.fps
            )
        
        # ROS2 publishers for control
        self.left_arm_pub = self.create_publisher(
            Float64MultiArray, config.left_arm_topic, 10)
        self.right_arm_pub = self.create_publisher(
            Float64MultiArray, config.right_arm_topic, 10)
        self.gripper_pub = self.create_publisher(
            Float64MultiArray, config.gripper_command_topic, 10)
        
        # ROS2 subscribers for state
        self._setup_subscribers()
        
        # Start ROS2 spinning in separate thread
        self.spin_thread = threading.Thread(target=self._spin_node, daemon=True)
        self.spin_thread.start()
        
        logger.info(f"YMrobot initialized with config: {config}")
        
    def _setup_subscribers(self):
        """Setup ROS2 subscribers for robot state."""
        # Joint state subscriber
        self.create_subscription(
            JointState, 
            self.config.joint_state_topic, 
            self._joint_state_callback, 
            qos_profile_sensor_data
        )
        
        # Gripper state subscriber
        self.create_subscription(
            Float64MultiArray,
            self.config.gripper_state_topic,
            self._gripper_state_callback,
            10
        )
        
        # Camera subscribers
        qos = QoSProfile(
            reliability=ReliabilityPolicy.BEST_EFFORT,
            history=HistoryPolicy.KEEP_LAST,
            depth=10
        )
        
        for cam_name, camera in self.cameras.items():
            self.create_subscription(
                Image,
                camera.topic,
                lambda msg, cam=camera: cam.update_image(msg),
                qos
            )
            
    def _spin_node(self):
        """Run ROS2 spin in separate thread."""
        try:
            rclpy.spin(self)
        except Exception as e:
            logger.error(f"ROS2 spin thread error: {e}")
            
    def _joint_state_callback(self, msg: JointState):
        """Callback for joint state updates."""
        self.joint_state = msg
        
    def _gripper_state_callback(self, msg: Float64MultiArray):
        """Callback for gripper state updates."""
        self.gripper_state = np.array(msg.data, dtype=np.float32)

    @property
    def _motors_ft(self) -> dict[str, type]:
        """Motor features for observation and action."""
        motors = {}
        # Left arm joints
        for joint_name in self.config.left_arm_joint_names:
            motors[f"{joint_name}.pos"] = float
        # Right arm joints
        for joint_name in self.config.right_arm_joint_names:
            motors[f"{joint_name}.pos"] = float
        # Grippers
        motors["left_gripper.pos"] = float
        motors["right_gripper.pos"] = float
        return motors

    @property
    def _cameras_ft(self) -> dict[str, tuple]:
        """Camera features for observation."""
        return {
            cam_name: (cam_config.height, cam_config.width, 3)
            for cam_name, cam_config in self.config.cameras.items()
        }

    @cached_property
    def observation_features(self) -> dict[str, type | tuple]:
        """Define observation features structure."""
        return {**self._motors_ft, **self._cameras_ft}

    @cached_property
    def action_features(self) -> dict[str, type]:
        """Define action features structure."""
        return self._motors_ft

    @property
    def is_connected(self) -> bool:
        """Check if robot is connected."""
        return (self._is_connected and
                all(cam.is_connected for cam in self.cameras.values()))

    @property
    def is_calibrated(self) -> bool:
        """YMrobot doesn't require calibration."""
        return self._is_calibrated

    def connect(self, calibrate: bool = True) -> None:
        """Connect to the robot."""
        try:
            # Connect cameras
            for camera in self.cameras.values():
                camera.connect()

            # Wait for initial data
            timeout = 5.0
            start_time = time.time()
            while (time.time() - start_time < timeout and
                   (self.joint_state is None or self.gripper_state is None)):
                time.sleep(0.1)

            if self.joint_state is None:
                logger.warning("No joint state received within timeout")
            if self.gripper_state is None:
                logger.warning("No gripper state received within timeout")

            self._is_connected = True
            logger.info("YMrobot connected successfully")

        except Exception as e:
            logger.error(f"Failed to connect YMrobot: {e}")
            raise

    def calibrate(self) -> None:
        """YMrobot calibration (no-op)."""
        pass

    def configure(self) -> None:
        """Configure YMrobot (no-op for ROS2-based robot)."""
        pass

    def disconnect(self) -> None:
        """Disconnect from the robot."""
        try:
            for camera in self.cameras.values():
                camera.disconnect()
            self._is_connected = False
            logger.info("YMrobot disconnected")
        except Exception as e:
            logger.error(f"Error during disconnect: {e}")

    def get_observation(self) -> dict[str, Any]:
        """Get current robot observation."""
        if not self.is_connected:
            raise DeviceNotConnectedError(f"{self} is not connected.")

        obs_dict = {}

        # Get joint positions
        if self.joint_state is not None:
            joint_positions = self._extract_joint_positions()
            obs_dict.update(joint_positions)
        else:
            logger.warning("No joint state available")

        # Get gripper positions
        if self.gripper_state is not None:
            obs_dict["left_gripper.pos"] = float(self.gripper_state[0])
            obs_dict["right_gripper.pos"] = float(self.gripper_state[1])
        else:
            logger.warning("No gripper state available")
            obs_dict["left_gripper.pos"] = 0.0
            obs_dict["right_gripper.pos"] = 0.0

        # Get camera images
        for cam_name, camera in self.cameras.items():
            image = camera.async_read()
            if image is not None:
                obs_dict[cam_name] = image
            else:
                logger.warning(f"No image available from {cam_name}")
                # Provide dummy image if none available
                height, width = camera.height, camera.width
                obs_dict[cam_name] = np.zeros((height, width, 3), dtype=np.uint8)

        return obs_dict

    def _extract_joint_positions(self) -> dict[str, float]:
        """Extract joint positions from joint state message."""
        positions = {}

        if self.joint_state is None:
            return positions

        # Create mapping from joint name to position
        name_to_pos = dict(zip(self.joint_state.name, self.joint_state.position))

        # Extract left arm positions
        for joint_name in self.config.left_arm_joint_names:
            if joint_name in name_to_pos:
                positions[f"{joint_name}.pos"] = float(name_to_pos[joint_name])
            else:
                logger.warning(f"Joint {joint_name} not found in joint state")
                positions[f"{joint_name}.pos"] = 0.0

        # Extract right arm positions
        for joint_name in self.config.right_arm_joint_names:
            if joint_name in name_to_pos:
                positions[f"{joint_name}.pos"] = float(name_to_pos[joint_name])
            else:
                logger.warning(f"Joint {joint_name} not found in joint state")
                positions[f"{joint_name}.pos"] = 0.0

        return positions

    def send_action(self, action: dict[str, Any]) -> dict[str, Any]:
        """Send action to the robot."""
        if not self.is_connected:
            raise DeviceNotConnectedError(f"{self} is not connected.")

        # Extract joint commands
        left_arm_commands = []
        right_arm_commands = []
        gripper_commands = []

        # Left arm commands
        for joint_name in self.config.left_arm_joint_names:
            key = f"{joint_name}.pos"
            if key in action:
                left_arm_commands.append(float(action[key]))
            else:
                logger.warning(f"Missing action for {key}")
                left_arm_commands.append(0.0)

        # Right arm commands
        for joint_name in self.config.right_arm_joint_names:
            key = f"{joint_name}.pos"
            if key in action:
                right_arm_commands.append(float(action[key]))
            else:
                logger.warning(f"Missing action for {key}")
                right_arm_commands.append(0.0)

        # Gripper commands
        left_gripper_cmd = float(action.get("left_gripper.pos", 0.0))
        right_gripper_cmd = float(action.get("right_gripper.pos", 0.0))
        gripper_commands = [left_gripper_cmd, right_gripper_cmd]

        # Publish commands
        try:
            # Left arm
            left_msg = Float64MultiArray()
            left_msg.data = left_arm_commands
            self.left_arm_pub.publish(left_msg)

            # Right arm
            right_msg = Float64MultiArray()
            right_msg.data = right_arm_commands
            self.right_arm_pub.publish(right_msg)

            # Grippers
            gripper_msg = Float64MultiArray()
            gripper_msg.data = gripper_commands
            self.gripper_pub.publish(gripper_msg)

            logger.debug(f"Sent action: left_arm={left_arm_commands}, "
                        f"right_arm={right_arm_commands}, grippers={gripper_commands}")

        except Exception as e:
            logger.error(f"Failed to send action: {e}")
            raise

        return action

    def __del__(self):
        """Cleanup when object is destroyed."""
        try:
            if hasattr(self, '_is_connected') and self._is_connected:
                self.disconnect()
            if hasattr(self, 'spin_thread') and self.spin_thread.is_alive():
                # Note: ROS2 node cleanup should be handled by the application
                pass
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    def __str__(self) -> str:
        return f"YMrobot({self.config.id or 'default'})"
