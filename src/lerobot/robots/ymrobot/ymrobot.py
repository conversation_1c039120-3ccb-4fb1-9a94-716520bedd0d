#!/usr/bin/env python

# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import logging
import time
import threading
from functools import cached_property
from typing import Any

import numpy as np
import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, HistoryPolicy, qos_profile_sensor_data
from sensor_msgs.msg import JointState, Image
from std_msgs.msg import Float64MultiArray
from cv_bridge import CvBridge
import cv2

from lerobot.cameras.utils import Camera
from lerobot.common.utils.utils import init_logging
from lerobot.robots.robot import Robot
from lerobot.robots.utils import DeviceNotConnectedError

from .config_ymrobot import YMrobotConfig

logger = logging.getLogger(__name__)


class YMrobotCamera(Camera):
    """Camera interface for YMrobot ROS2 image topics."""
    
    def __init__(self, topic: str, width: int, height: int, fps: int):
        self.topic = topic
        self.width = width
        self.height = height
        self.fps = fps
        self.bridge = CvBridge()
        self.latest_image = None
        self.image_timestamp = None
        self._is_connected = False
        
    def connect(self):
        """Connect to the camera (handled by YMrobot node)."""
        self._is_connected = True
        
    def disconnect(self):
        """Disconnect from the camera."""
        self._is_connected = False
        
    @property
    def is_connected(self) -> bool:
        return self._is_connected
        
    def async_read(self) -> np.ndarray | None:
        """Return the latest image."""
        if self.latest_image is None:
            return None
        return self.latest_image.copy()
        
    def update_image(self, image_msg):
        """Update the latest image from ROS2 message."""
        try:
            # Convert ROS2 image to OpenCV format (BGR)
            cv_image = self.bridge.imgmsg_to_cv2(image_msg, 'bgr8')
            
            # Pad image to target size if needed
            if cv_image.shape[:2] != (self.height, self.width):
                cv_image = self._pad_image(cv_image)
                
            # Convert BGR to RGB for LeRobot
            self.latest_image = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)
            self.image_timestamp = image_msg.header.stamp
        except Exception as e:
            logger.warning(f"Failed to convert image from topic {self.topic}: {e}")
            
    def _pad_image(self, img):
        """Pad image to target size, center it."""
        h, w = img.shape[:2]
        target_h, target_w = self.height, self.width
        top = (target_h - h) // 2
        bottom = target_h - h - top
        left = (target_w - w) // 2
        right = target_w - w - left
        return cv2.copyMakeBorder(img, top, bottom, left, right,
                                  cv2.BORDER_CONSTANT, value=[0, 0, 0])


class YMrobot(Robot, Node):
    """
    YMrobot dual-arm robot with ROS2 interface for LeRobot framework.
    
    This robot features:
    - Dual 7-DOF arms (left and right)
    - Dual grippers with RS485 control
    - Multiple cameras (head, left wrist, right wrist)
    - ROS2 communication interface
    """
    
    config_class = YMrobotConfig
    name = "ymrobot"
    
    def __init__(self, config: YMrobotConfig):
        # Initialize Robot base class
        Robot.__init__(self, config)
        
        # Initialize ROS2 node
        if not rclpy.ok():
            rclpy.init()
        Node.__init__(self, config.ros2_node_name)
        
        self.config = config
        self._is_connected = False
        self._is_calibrated = True  # YMrobot doesn't require calibration
        
        # Joint state storage
        self.joint_state = None
        self.gripper_state = None
        
        # Initialize cameras
        self.cameras = {}
        for cam_name, cam_config in config.cameras.items():
            topic = getattr(config, f"{cam_name}_topic")
            self.cameras[cam_name] = YMrobotCamera(
                topic=topic,
                width=cam_config.width,
                height=cam_config.height,
                fps=cam_config.fps
            )
        
        # ROS2 publishers for control
        self.left_arm_pub = self.create_publisher(
            Float64MultiArray, config.left_arm_topic, 10)
        self.right_arm_pub = self.create_publisher(
            Float64MultiArray, config.right_arm_topic, 10)
        self.gripper_pub = self.create_publisher(
            Float64MultiArray, config.gripper_command_topic, 10)
        
        # ROS2 subscribers for state
        self._setup_subscribers()
        
        # Start ROS2 spinning in separate thread
        self.spin_thread = threading.Thread(target=self._spin_node, daemon=True)
        self.spin_thread.start()
        
        logger.info(f"YMrobot initialized with config: {config}")
        
    def _setup_subscribers(self):
        """Setup ROS2 subscribers for robot state."""
        # Joint state subscriber
        self.create_subscription(
            JointState, 
            self.config.joint_state_topic, 
            self._joint_state_callback, 
            qos_profile_sensor_data
        )
        
        # Gripper state subscriber
        self.create_subscription(
            Float64MultiArray,
            self.config.gripper_state_topic,
            self._gripper_state_callback,
            10
        )
        
        # Camera subscribers
        qos = QoSProfile(
            reliability=ReliabilityPolicy.BEST_EFFORT,
            history=HistoryPolicy.KEEP_LAST,
            depth=10
        )
        
        for cam_name, camera in self.cameras.items():
            self.create_subscription(
                Image,
                camera.topic,
                lambda msg, cam=camera: cam.update_image(msg),
                qos
            )
            
    def _spin_node(self):
        """Run ROS2 spin in separate thread."""
        try:
            rclpy.spin(self)
        except Exception as e:
            logger.error(f"ROS2 spin thread error: {e}")
            
    def _joint_state_callback(self, msg: JointState):
        """Callback for joint state updates."""
        self.joint_state = msg
        
    def _gripper_state_callback(self, msg: Float64MultiArray):
        """Callback for gripper state updates."""
        self.gripper_state = np.array(msg.data, dtype=np.float32)
