# YMrobot

YMrobot is a dual-arm humanoid robot with ROS2 interface, now integrated into the LeRobot framework.

## Features

- **Dual 7-DOF Arms**: Left and right arms with 7 joints each
- **Dual Grippers**: RS485-controlled grippers for precise manipulation
- **Multi-Camera System**: Head camera and wrist cameras for comprehensive vision
- **ROS2 Interface**: Native ROS2 communication for real-time control
- **Safety Features**: Joint limits and velocity checking for safe operation

## Hardware Specifications

### Arms
- **Left Arm**: 7 joints (Left_Arm_Joint1 to Left_Arm_Joint7)
- **Right Arm**: 7 joints (Right_Arm_Joint1 to Right_Arm_Joint7)
- **Control Interface**: ROS2 position controllers
- **Joint Limits**: Hardware-enforced safety limits

### Grippers
- **Type**: RS485-controlled grippers
- **Ports**: /dev/ttyUSB0 (left), /dev/ttyUSB1 (right)
- **Control**: Position-based control with feedback

### Cameras
- **Head Camera**: 640x480 @ 30fps (topic: /camera/color/image_raw)
- **Left Wrist Camera**: 640x480 @ 30fps (topic: /camera1/camera1/color/image_raw)
- **Right Wrist Camera**: 640x480 @ 30fps (topic: /camera2/camera2/color/image_raw)

## Installation

### Prerequisites

1. **ROS2 Humble**: Ensure ROS2 Humble is installed and sourced
2. **YMrobot ROS2 Package**: Your YMrobot ROS2 control system should be running
3. **LeRobot**: Install LeRobot framework

### Setup

1. The YMrobot integration is already included in LeRobot
2. Ensure your ROS2 environment is properly sourced:
   ```bash
   source /opt/ros/humble/setup.bash
   source /path/to/your/ymrobot_ws/install/setup.bash
   ```

## Configuration

### Basic Configuration

```python
from lerobot.robots.ymrobot import YMrobotConfig
from lerobot.cameras import CameraConfig

config = YMrobotConfig(
    id="my_ymrobot",
    cameras={
        "head_camera": CameraConfig(width=640, height=480, fps=30),
        "left_wrist_camera": CameraConfig(width=640, height=480, fps=30),
        "right_wrist_camera": CameraConfig(width=640, height=480, fps=30),
    }
)
```

### Advanced Configuration

```python
config = YMrobotConfig(
    id="advanced_ymrobot",
    # ROS2 topics
    left_arm_topic="/left_arm_position_controller/commands",
    right_arm_topic="/right_arm_position_controller/commands",
    joint_state_topic="/joint_states",
    gripper_state_topic="/gripper_states",
    
    # Safety settings
    max_joint_change_deg=30.0,
    enable_joint_limits=True,
    enable_velocity_check=True,
    
    # Control frequency
    control_frequency=50.0,
    
    # Camera configurations
    cameras={
        "head_camera": CameraConfig(width=1280, height=720, fps=60),
        "left_wrist_camera": CameraConfig(width=640, height=480, fps=30),
        "right_wrist_camera": CameraConfig(width=640, height=480, fps=30),
    }
)
```

## Usage Examples

### Recording Data

```bash
lerobot-record \
    --robot.type=ymrobot \
    --robot.id=my_ymrobot \
    --dataset.repo_id=my_username/ymrobot_dataset \
    --dataset.num_episodes=10 \
    --dataset.single_task="Pick and place objects"
```

### Replaying Episodes

```bash
lerobot-replay \
    --robot.type=ymrobot \
    --robot.id=my_ymrobot \
    --dataset.repo_id=my_username/ymrobot_dataset \
    --dataset.episode=0
```

### Teleoperation

```bash
lerobot-teleoperate \
    --robot.type=ymrobot \
    --robot.id=my_ymrobot \
    --teleop.type=keyboard
```

### Python API

```python
from lerobot.robots.utils import make_robot_from_config
from lerobot.robots.ymrobot import YMrobotConfig

# Create configuration
config = YMrobotConfig(id="my_ymrobot")

# Create robot instance
robot = make_robot_from_config(config)

# Connect to robot
robot.connect()

# Get observation
obs = robot.get_observation()
print(f"Joint positions: {obs}")

# Send action
action = {
    "Left_Arm_Joint1.pos": 0.1,
    "Left_Arm_Joint2.pos": 0.2,
    # ... other joints
    "left_gripper.pos": 0.5,
    "right_gripper.pos": 0.7,
}
robot.send_action(action)

# Disconnect
robot.disconnect()
```

## Data Format

### Observation Features

The robot provides the following observation features:

- **Joint Positions**: 14 arm joints + 2 gripper positions (16 total)
  - `Left_Arm_Joint1.pos` to `Left_Arm_Joint7.pos`
  - `Right_Arm_Joint1.pos` to `Right_Arm_Joint7.pos`
  - `left_gripper.pos`, `right_gripper.pos`

- **Camera Images**: 3 cameras with shape (480, 640, 3)
  - `head_camera`
  - `left_wrist_camera`
  - `right_wrist_camera`

### Action Features

Actions have the same structure as joint positions in observations:
- 14 arm joint positions + 2 gripper positions (16 total)

## Troubleshooting

### Common Issues

1. **ROS2 Not Available**
   ```
   ImportError: ROS2 is not available for YMrobot
   ```
   **Solution**: Ensure ROS2 is properly installed and sourced

2. **No Joint State Data**
   ```
   WARNING: No joint state available
   ```
   **Solution**: Check that your YMrobot ROS2 system is publishing to `/joint_states`

3. **Camera Connection Issues**
   ```
   WARNING: No image available from head_camera
   ```
   **Solution**: Verify camera topics are publishing and accessible

### Debugging

Enable debug logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

Check ROS2 topics:
```bash
ros2 topic list
ros2 topic echo /joint_states
ros2 topic echo /gripper_states
```

## Integration with Your YMrobot System

To use this integration with your existing YMrobot system:

1. Ensure your ROS2 system publishes the required topics:
   - `/joint_states` (sensor_msgs/JointState)
   - `/gripper_states` (std_msgs/Float64MultiArray)
   - Camera topics (sensor_msgs/Image)

2. Ensure your ROS2 system subscribes to control topics:
   - `/left_arm_position_controller/commands` (std_msgs/Float64MultiArray)
   - `/right_arm_position_controller/commands` (std_msgs/Float64MultiArray)
   - `/gripper_commands` (std_msgs/Float64MultiArray)

3. Update topic names in configuration if needed

## Contributing

To extend or modify the YMrobot integration:

1. Edit `src/lerobot/robots/ymrobot/ymrobot.py` for robot implementation
2. Edit `src/lerobot/robots/ymrobot/config_ymrobot.py` for configuration
3. Run tests: `python test_ymrobot_simple.py`
4. Submit pull requests for improvements
