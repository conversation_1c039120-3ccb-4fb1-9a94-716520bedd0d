#!/usr/bin/env python

# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from dataclasses import dataclass, field

from lerobot.cameras import CameraConfig

from ..config import RobotConfig


@RobotConfig.register_subclass("ymrobot")
@dataclass
class YMrobotConfig(RobotConfig):
    """Configuration for YMrobot dual-arm robot with ROS2 interface."""
    
    # ROS2 node configuration
    ros2_node_name: str = "ymrobot_lerobot_interface"
    
    # Joint control topics
    left_arm_topic: str = "/left_arm_position_controller/commands"
    right_arm_topic: str = "/right_arm_position_controller/commands"
    gripper_command_topic: str = "/gripper_commands"
    
    # State topics
    joint_state_topic: str = "/joint_states"
    gripper_state_topic: str = "/gripper_states"
    
    # Camera topics
    head_camera_topic: str = "/camera/color/image_raw"
    left_wrist_camera_topic: str = "/camera1/camera1/color/image_raw"
    right_wrist_camera_topic: str = "/camera2/camera2/color/image_raw"
    
    # Gripper configuration
    left_gripper_port: str = "/dev/ttyUSB0"
    right_gripper_port: str = "/dev/ttyUSB1"
    gripper_node_id: int = 1
    
    # Safety configuration
    max_joint_change_deg: float = 30.0
    enable_joint_limits: bool = True
    enable_velocity_check: bool = True
    
    # Control frequency
    control_frequency: float = 50.0  # Hz
    
    # Joint names (in order)
    left_arm_joint_names: list[str] = field(default_factory=lambda: [
        "Left_Arm_Joint1", "Left_Arm_Joint2", "Left_Arm_Joint3", "Left_Arm_Joint4",
        "Left_Arm_Joint5", "Left_Arm_Joint6", "Left_Arm_Joint7"
    ])
    
    right_arm_joint_names: list[str] = field(default_factory=lambda: [
        "Right_Arm_Joint1", "Right_Arm_Joint2", "Right_Arm_Joint3", "Right_Arm_Joint4",
        "Right_Arm_Joint5", "Right_Arm_Joint6", "Right_Arm_Joint7"
    ])
    
    # Cameras configuration
    cameras: dict[str, CameraConfig] = field(default_factory=dict)
    
    # Default camera configurations
    def __post_init__(self):
        super().__post_init__()
        
        # Set default camera configurations if not provided
        if not self.cameras:
            self.cameras = {
                "head_camera": CameraConfig(
                    width=640,
                    height=480,
                    fps=30,
                ),
                "left_wrist_camera": CameraConfig(
                    width=640,
                    height=480,
                    fps=30,
                ),
                "right_wrist_camera": CameraConfig(
                    width=640,
                    height=480,
                    fps=30,
                ),
            }
