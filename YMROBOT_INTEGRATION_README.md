# YMrobot Integration with LeRobot Framework

This document describes the successful integration of YMrobot into the LeRobot framework.

## Overview

YMrobot is now fully integrated into the LeRobot framework, enabling:
- Data collection and recording
- Episode replay
- Teleoperation
- Policy training and evaluation
- Seamless integration with LeRobot's ecosystem

## What Was Implemented

### 1. Robot Configuration (`src/lerobot/robots/ymrobot/config_ymrobot.py`)
- `YMrobotConfig` class with comprehensive configuration options
- Camera configurations for head and wrist cameras
- ROS2 topic configurations
- Safety parameters and control settings
- Gripper and joint configurations

### 2. Robot Implementation (`src/lerobot/robots/ymrobot/ymrobot.py`)
- `YMrobot` class inheriting from LeRobot's `Robot` base class
- `YMrobotCamera` class for ROS2 image handling
- Complete implementation of all required abstract methods:
  - `observation_features` and `action_features`
  - `connect()`, `disconnect()`, `calibrate()`, `configure()`
  - `get_observation()` and `send_action()`
- ROS2 integration with optional imports for compatibility
- Safety features and error handling

### 3. Framework Integration
- Updated `src/lerobot/robots/utils.py` to register YMrobot
- Updated `src/lerobot/__init__.py` to include YMrobot in available robots
- Updated all main LeRobot scripts to import YMrobot:
  - `teleoperate.py`
  - `record.py`
  - `replay.py`
  - `calibrate.py`
  - `setup_motors.py`

### 4. Module Structure
- Created `src/lerobot/robots/ymrobot/` directory
- Added `__init__.py` for proper module exports
- Organized code following LeRobot conventions

## Robot Specifications

### Hardware
- **Dual Arms**: 7-DOF left and right arms (14 joints total)
- **Grippers**: RS485-controlled left and right grippers
- **Cameras**: Head camera + left/right wrist cameras
- **Interface**: ROS2-based communication

### Data Format
- **Observations**: 16 motor positions + 3 camera images
- **Actions**: 16 motor position commands
- **Cameras**: 640x480 RGB images (configurable)

### ROS2 Topics
- **Control**: `/left_arm_position_controller/commands`, `/right_arm_position_controller/commands`
- **State**: `/joint_states`, `/gripper_states`
- **Cameras**: `/camera/color/image_raw`, `/camera1/camera1/color/image_raw`, `/camera2/camera2/color/image_raw`

## Usage Examples

### Recording Data
```bash
lerobot-record \
    --robot.type=ymrobot \
    --robot.id=my_ymrobot \
    --dataset.repo_id=username/ymrobot_dataset \
    --dataset.num_episodes=10
```

### Replaying Episodes
```bash
lerobot-replay \
    --robot.type=ymrobot \
    --robot.id=my_ymrobot \
    --dataset.repo_id=username/ymrobot_dataset \
    --dataset.episode=0
```

### Python API
```python
from lerobot.robots.utils import make_robot_from_config
from lerobot.robots.ymrobot import YMrobotConfig

config = YMrobotConfig(id="my_ymrobot")
robot = make_robot_from_config(config)
robot.connect()

# Get observation
obs = robot.get_observation()

# Send action
action = {
    "Left_Arm_Joint1.pos": 0.1,
    "Right_Arm_Joint1.pos": -0.1,
    "left_gripper.pos": 0.5,
    "right_gripper.pos": 0.5,
}
robot.send_action(action)
```

## Testing

### Test Results
All integration tests pass successfully:
- ✓ YMrobot configuration creation
- ✓ Robot registration in LeRobot framework
- ✓ Robot class attributes and structure
- ✓ `make_robot_from_config` function
- ✓ Action and observation features

### Running Tests
```bash
# Basic integration test (no ROS2 required)
python test_ymrobot_simple.py

# Full integration test (requires ROS2)
python test_ymrobot_integration.py
```

## Files Created/Modified

### New Files
- `src/lerobot/robots/ymrobot/config_ymrobot.py`
- `src/lerobot/robots/ymrobot/ymrobot.py`
- `src/lerobot/robots/ymrobot/__init__.py`
- `src/lerobot/robots/ymrobot/ymrobot.mdx`
- `examples/ymrobot_example.py`
- `test_ymrobot_simple.py`
- `test_ymrobot_integration.py`

### Modified Files
- `src/lerobot/robots/utils.py` - Added YMrobot registration
- `src/lerobot/__init__.py` - Added YMrobot to available robots
- `src/lerobot/teleoperate.py` - Added YMrobot import
- `src/lerobot/record.py` - Added YMrobot import
- `src/lerobot/replay.py` - Added YMrobot import
- `src/lerobot/calibrate.py` - Added YMrobot import
- `src/lerobot/setup_motors.py` - Added YMrobot import

## Integration with Your YMrobot System

To use this integration with your existing YMrobot system:

1. **Ensure ROS2 Topics**: Your system should publish/subscribe to the expected topics
2. **Start YMrobot System**: Launch your ROS2 control system
3. **Source Environment**: Make sure ROS2 is properly sourced
4. **Use LeRobot Commands**: Use standard LeRobot commands with `--robot.type=ymrobot`

## Next Steps

1. **Test with Hardware**: Connect to your actual YMrobot system
2. **Collect Data**: Record demonstration episodes
3. **Train Policies**: Use LeRobot's policy training capabilities
4. **Deploy**: Run trained policies on your robot

## Troubleshooting

### Common Issues
1. **ROS2 Not Available**: Ensure ROS2 is installed and sourced
2. **Topic Not Found**: Check that your YMrobot system is publishing expected topics
3. **Connection Failed**: Verify ROS2 communication is working

### Debug Commands
```bash
# Check ROS2 topics
ros2 topic list

# Monitor joint states
ros2 topic echo /joint_states

# Test camera feeds
ros2 topic echo /camera/color/image_raw
```

## Conclusion

YMrobot has been successfully integrated into the LeRobot framework with full feature support. The integration follows LeRobot's architecture and conventions, making it compatible with all LeRobot tools and workflows.

The robot can now be used for:
- Data collection and dataset creation
- Policy training and evaluation
- Real-time teleoperation
- Episode replay and analysis
- Integration with LeRobot's ecosystem of tools

This integration enables YMrobot users to leverage the full power of the LeRobot framework for robotics research and development.
