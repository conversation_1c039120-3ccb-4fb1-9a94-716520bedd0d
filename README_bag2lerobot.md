# ROS2 Bag to LeRobotDataset Converter

这个脚本可以将ROS2 bag录制的数据转换为LeRobotDataset格式，支持双臂机器人的数据转换。

## 功能特性

- 支持多个摄像头的图像数据转换
- 智能图像尺寸处理（保持宽高比的填充）
- 处理关节状态和夹爪状态
- 转换动作命令数据
- 基于关节状态的时间戳同步和数据对齐
- 可配置的同步容忍度处理不同频率的话题
- 支持两种后端：rosbags（纯Python）和ros2py（需要ROS2环境）

## 安装依赖

### 方法1：使用rosbags后端（推荐）
```bash
pip install rosbags==0.10.5  # 纯Python读取rosbag2
pip install pillow numpy tqdm
```

### 方法2：使用ros2py后端
```bash
# 需要ROS2环境
pip install rosbag2-py rclpy sensor-msgs std-msgs cv-bridge
```

## 使用方法

### 基本用法
```bash
python3 bag2lerobot.py \
    --input /path/to/bags_directory \
    --output /path/to/output/lerobot_dataset \
    --repo-id my_dual_arm_dataset \
    --fps 30 \
    --robot-type dual_arm_robot
```

### 完整参数示例
```bash
python3 bag2lerobot.py \
    --input /path/to/bags_directory \
    --output /path/to/output/lerobot_dataset \
    --repo-id my_dual_arm_dataset \
    --fps 30 \
    --robot-type dual_arm_robot \
    --camera-head /camera/color/image_raw \
    --camera-left /camera1/camera1/color/image_raw \
    --camera-right /camera2/camera2/color/image_raw \
    --joint-states /joint_states \
    --gripper-states /gripper_states \
    --left-action /left_arm_position_controller/commands \
    --right-action /right_arm_position_controller/commands \
    --gripper-action /gripper_commands \
    --backend rosbags \
    --resize 640x480 \
    --sync-tolerance 100
```

## 参数说明

- `--input`: 包含多个rosbag2文件夹的目录
- `--output`: 输出LeRobotDataset的目录
- `--repo-id`: 数据集的仓库ID（必需）
- `--fps`: 数据集的帧率（默认30）
- `--robot-type`: 机器人类型标识符（默认dual_arm_robot）
- `--camera-*`: 各摄像头话题名称
- `--joint-states`: 关节状态话题名称
- `--gripper-states`: 夹爪状态话题名称
- `--*-action`: 各种动作命令话题名称
- `--backend`: 后端选择（rosbags或ros2py）
- `--resize`: 图像尺寸调整（如640x480，保持宽高比并填充）
- `--sync-tolerance`: 同步容忍度（毫秒，默认100ms）

## 数据格式

### 输入ROS2话题
- `/camera/color/image_raw` (sensor_msgs/Image): 头部摄像头
- `/camera1/camera1/color/image_raw` (sensor_msgs/Image): 左手摄像头
- `/camera2/camera2/color/image_raw` (sensor_msgs/Image): 右手摄像头
- `/joint_states` (sensor_msgs/JointState): 14个关节位置
- `/gripper_states` (std_msgs/Float64MultiArray): 2个夹爪位置
- `/left_arm_position_controller/commands` (std_msgs/Float64MultiArray): 左臂动作
- `/right_arm_position_controller/commands` (std_msgs/Float64MultiArray): 右臂动作
- `/gripper_commands` (std_msgs/Float64MultiArray): 夹爪动作

### 输出LeRobotDataset格式
- `observation.images.head`: 头部图像
- `observation.images.left_hand`: 左手图像
- `observation.images.right_hand`: 右手图像
- `observation.state`: 14个关节位置
- `observation.gripper_state`: 2个夹爪位置
- `action`: 16维动作向量（7+7+2）

## 验证转换结果

```python
from lerobot.datasets.lerobot_dataset import LeRobotDataset

# 加载转换后的数据集
dataset = LeRobotDataset("/path/to/output/lerobot_dataset")
print(f"Dataset: {dataset}")
print(f"Features: {dataset.features}")
print(f"Episodes: {dataset.num_episodes}")
print(f"Frames: {dataset.num_frames}")

# 查看第一个样本
sample = dataset[0]
print(f"Sample keys: {sample.keys()}")
```

## 时间同步策略

脚本使用以下策略处理不同频率的话题：

1. **主时间线**: 以`/joint_states`话题的时间戳作为主时间线
2. **最近邻匹配**: 对于其他话题（图像、动作等），在容忍度范围内寻找最近的消息
3. **容忍度控制**: 通过`--sync-tolerance`参数控制同步容忍度（默认100ms）
4. **图像处理**:
   - 保持原始宽高比
   - 缩放到目标尺寸内
   - 用黑色填充剩余区域
5. **缺失数据**: 超出容忍度或缺失的数据用零值/黑色图像填充

## 注意事项

1. 确保所有必需的话题都存在于bag文件中
2. 关节名称必须按照指定顺序：Left_Arm_Joint1-7, Right_Arm_Joint1-7
3. 图像编码支持rgb8、bgr8、rgba8、bgra8、mono8等格式
4. 建议根据数据采集频率调整同步容忍度
5. 对于高频差异的数据，可能需要增大容忍度

## 故障排除

1. **导入错误**: 确保安装了正确的依赖包
2. **话题不存在**: 检查bag文件中的话题名称是否正确
3. **图像解码失败**: 检查图像编码格式是否支持
4. **内存不足**: 对于大型数据集，考虑分批处理
