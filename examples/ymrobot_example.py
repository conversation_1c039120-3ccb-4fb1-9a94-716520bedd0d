#!/usr/bin/env python3
"""
YMrobot Example Script for LeRobot Framework

This script demonstrates how to use YMrobot with the LeRobot framework.
It shows basic robot control, data collection, and observation retrieval.

Prerequisites:
1. ROS2 Humble installed and sourced
2. YMrobot ROS2 system running
3. LeRobot framework installed

Usage:
    python examples/ymrobot_example.py
"""

import time
import numpy as np
from pathlib import Path
import sys

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from lerobot.robots.utils import make_robot_from_config
from lerobot.robots.ymrobot import YMrobotConfig
from lerobot.cameras import CameraConfig


def create_ymrobot_config():
    """Create a YMrobot configuration."""
    config = YMrobotConfig(
        id="example_ymrobot",
        # Camera configurations
        cameras={
            "head_camera": CameraConfig(width=640, height=480, fps=30),
            "left_wrist_camera": CameraConfig(width=640, height=480, fps=30),
            "right_wrist_camera": CameraConfig(width=640, height=480, fps=30),
        },
        # Safety settings
        max_joint_change_deg=30.0,
        enable_joint_limits=True,
        enable_velocity_check=True,
        # Control frequency
        control_frequency=50.0,
    )
    return config


def demonstrate_robot_connection():
    """Demonstrate robot connection and basic functionality."""
    print("=" * 60)
    print("YMrobot LeRobot Integration Example")
    print("=" * 60)
    
    # Create configuration
    print("1. Creating YMrobot configuration...")
    config = create_ymrobot_config()
    print(f"   Robot ID: {config.id}")
    print(f"   Control frequency: {config.control_frequency} Hz")
    print(f"   Cameras: {list(config.cameras.keys())}")
    
    # Create robot instance
    print("\n2. Creating robot instance...")
    try:
        robot = make_robot_from_config(config)
        print(f"   Robot created: {robot}")
        print(f"   Robot type: {robot.name}")
    except ImportError as e:
        print(f"   Error: {e}")
        print("   Make sure ROS2 is properly installed and sourced.")
        return None
    
    # Connect to robot
    print("\n3. Connecting to robot...")
    try:
        robot.connect()
        print(f"   Connected: {robot.is_connected}")
        print(f"   Calibrated: {robot.is_calibrated}")
    except Exception as e:
        print(f"   Connection failed: {e}")
        print("   Make sure YMrobot ROS2 system is running.")
        return None
    
    return robot


def demonstrate_observation_features(robot):
    """Demonstrate observation features."""
    print("\n4. Observation Features:")
    obs_features = robot.observation_features
    
    print("   Motor features:")
    for key, value in obs_features.items():
        if key.endswith('.pos'):
            print(f"     {key}: {value}")
    
    print("   Camera features:")
    for key, value in obs_features.items():
        if not key.endswith('.pos'):
            print(f"     {key}: {value}")


def demonstrate_action_features(robot):
    """Demonstrate action features."""
    print("\n5. Action Features:")
    action_features = robot.action_features
    
    print(f"   Total action dimensions: {len(action_features)}")
    print("   Action keys:")
    for key in action_features.keys():
        print(f"     {key}")


def demonstrate_get_observation(robot):
    """Demonstrate getting observations from the robot."""
    print("\n6. Getting Robot Observation:")
    
    try:
        obs = robot.get_observation()
        
        print("   Joint positions:")
        for key, value in obs.items():
            if key.endswith('.pos'):
                print(f"     {key}: {value:.3f}")
        
        print("   Camera images:")
        for key, value in obs.items():
            if not key.endswith('.pos'):
                if hasattr(value, 'shape'):
                    print(f"     {key}: shape {value.shape}, dtype {value.dtype}")
                else:
                    print(f"     {key}: {type(value)}")
                    
    except Exception as e:
        print(f"   Failed to get observation: {e}")


def demonstrate_send_action(robot):
    """Demonstrate sending actions to the robot."""
    print("\n7. Sending Robot Action:")
    
    try:
        # Create a safe action (small movements)
        action = {}
        
        # Left arm - small movements
        left_joints = robot.config.left_arm_joint_names
        for i, joint_name in enumerate(left_joints):
            action[f"{joint_name}.pos"] = 0.1 * np.sin(i * 0.5)  # Small sinusoidal movements
        
        # Right arm - small movements
        right_joints = robot.config.right_arm_joint_names
        for i, joint_name in enumerate(right_joints):
            action[f"{joint_name}.pos"] = 0.1 * np.cos(i * 0.5)  # Small cosine movements
        
        # Grippers - moderate opening
        action["left_gripper.pos"] = 0.5
        action["right_gripper.pos"] = 0.5
        
        print("   Sending action...")
        result = robot.send_action(action)
        print("   Action sent successfully!")
        
        # Wait a bit for the action to take effect
        time.sleep(1.0)
        
    except Exception as e:
        print(f"   Failed to send action: {e}")


def demonstrate_data_collection_loop(robot, duration=5.0):
    """Demonstrate a simple data collection loop."""
    print(f"\n8. Data Collection Loop ({duration}s):")
    
    start_time = time.time()
    observations = []
    
    try:
        while time.time() - start_time < duration:
            # Get observation
            obs = robot.get_observation()
            observations.append(obs)
            
            # Create a simple action (gentle waving motion)
            t = time.time() - start_time
            action = {}
            
            # Left arm gentle wave
            for i, joint_name in enumerate(robot.config.left_arm_joint_names):
                if i == 1:  # Second joint for waving
                    action[f"{joint_name}.pos"] = 0.2 * np.sin(2 * np.pi * 0.5 * t)
                else:
                    action[f"{joint_name}.pos"] = 0.0
            
            # Right arm stays still
            for joint_name in robot.config.right_arm_joint_names:
                action[f"{joint_name}.pos"] = 0.0
            
            # Grippers
            action["left_gripper.pos"] = 0.5
            action["right_gripper.pos"] = 0.5
            
            # Send action
            robot.send_action(action)
            
            # Control frequency
            time.sleep(1.0 / robot.config.control_frequency)
        
        print(f"   Collected {len(observations)} observations")
        print("   Data collection completed successfully!")
        
    except Exception as e:
        print(f"   Data collection failed: {e}")


def main():
    """Main example function."""
    robot = None
    
    try:
        # Demonstrate robot connection
        robot = demonstrate_robot_connection()
        if robot is None:
            return
        
        # Demonstrate features
        demonstrate_observation_features(robot)
        demonstrate_action_features(robot)
        
        # Demonstrate robot interaction
        demonstrate_get_observation(robot)
        demonstrate_send_action(robot)
        
        # Demonstrate data collection
        demonstrate_data_collection_loop(robot, duration=3.0)
        
        print("\n" + "=" * 60)
        print("YMrobot example completed successfully!")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\nExample interrupted by user.")
        
    except Exception as e:
        print(f"\nExample failed with error: {e}")
        
    finally:
        # Clean up
        if robot is not None:
            try:
                robot.disconnect()
                print("Robot disconnected.")
            except:
                pass


if __name__ == "__main__":
    main()
