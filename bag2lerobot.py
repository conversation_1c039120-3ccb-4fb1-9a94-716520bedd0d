#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Convert ROS2 bag files to LeRobotDataset format.

This script converts ROS2 bag recordings to the LeRobotDataset format, supporting:
- Image topics from multiple cameras
- Joint state data
- Gripper state data
- Action commands for arms and grippers

Installation:
    pip install rosbags==0.10.5  # Pure Python reader for rosbag2
    pip install pillow numpy tqdm
    # Or for ROS2 environment: pip install rosbag2-py rclpy sensor-msgs std-msgs cv-bridge

Usage:
    python3 bag2lerobot.py \
        --input /path/to/bags_dir \
        --output /path/to/output/lerobot_dataset \
        --repo-id my_dataset \
        --fps 30 \
        --robot-type dual_arm_robot \
        --camera-head /camera/color/image_raw \
        --camera-left /camera1/camera1/color/image_raw \
        --camera-right /camera2/camera2/color/image_raw \
        --joint-states /joint_states \
        --gripper-states /gripper_states \
        --left-action /left_arm_position_controller/commands \
        --right-action /right_arm_position_controller/commands \
        --gripper-action /gripper_commands \
        --backend rosbags

Validation:
    python3 -c "from lerobot.datasets.lerobot_dataset import LeRobotDataset; ds=LeRobotDataset('/path/to/output/lerobot_dataset'); print(ds)"
"""



"""
Convert ROS2 bag recordings into a LeRobotDataset-style HuggingFace Dataset.

- Supports two backends:
  1) rosbags (default): pure-Python reader for rosbag2 (db3/mcap)
  2) ros2py: use rosbag2_py (requires ROS2 env)
- Sync policy: align samples on /joint_states timestamps by nearest-neighbor
  (optional linear interpolation for actions; images are nearest-neighbor).
- Output schema (single split 'train'):
  {
    'observation': {
      'images': {
        'head': Image(), 'left': Image(), 'right': Image()
      },
      'qpos': Sequence(float, length=14)
    },
    'action': Sequence(float, length=16),
    'timestamp': int64,                # nanoseconds
    'is_terminal': bool,
    'episode_id': int32
  }
"""

import os
import glob
import argparse
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple

import numpy as np
from PIL import Image
from tqdm import tqdm

# LeRobot imports
from lerobot.datasets.lerobot_dataset import LeRobotDataset

# -----------------------------
# Configuration
# -----------------------------

JOINT_ORDER = [
    'Left_Arm_Joint1','Left_Arm_Joint2','Left_Arm_Joint3','Left_Arm_Joint4',
    'Left_Arm_Joint5','Left_Arm_Joint6','Left_Arm_Joint7',
    'Right_Arm_Joint1','Right_Arm_Joint2','Right_Arm_Joint3','Right_Arm_Joint4',
    'Right_Arm_Joint5','Right_Arm_Joint6','Right_Arm_Joint7'
]

@dataclass
class Topics:
    cam_head: str
    cam_left: str
    cam_right: str
    joint_states: str
    gripper_states: str
    left_action: str
    right_action: str
    gripper_action: str


def get_lerobot_features(image_shape: Tuple[int, int, int] = (480, 640, 3)) -> Dict[str, Dict]:
    """Define LeRobotDataset features structure."""
    features = {
        # Observation features
        "observation.images.head": {
            "dtype": "video",
            "shape": image_shape,
            "names": ["height", "width", "channels"],
        },
        "observation.images.left_hand": {
            "dtype": "video",
            "shape": image_shape,
            "names": ["height", "width", "channels"],
        },
        "observation.images.right_hand": {
            "dtype": "video",
            "shape": image_shape,
            "names": ["height", "width", "channels"],
        },
        "observation.state": {
            "dtype": "float32",
            "shape": (14,),  # 14 joint positions
            "names": JOINT_ORDER,
        },
        "observation.gripper_state": {
            "dtype": "float32",
            "shape": (2,),  # left and right gripper positions
            "names": ["left_gripper", "right_gripper"],
        },
        # Action features - combined into single action vector
        "action": {
            "dtype": "float32",
            "shape": (16,),  # 7 + 7 + 2 (left_arm + right_arm + gripper)
            "names": [f"left_joint_{i+1}" for i in range(7)] +
                     [f"right_joint_{i+1}" for i in range(7)] +
                     ["left_gripper_cmd", "right_gripper_cmd"],
        },
    }
    return features

# -----------------------------
# Utilities
# -----------------------------

def ensure_dir(p: str):
    os.makedirs(p, exist_ok=True)

def ns_to_int(ns):
    return int(ns)

def nearest_idx(sorted_ts: np.ndarray, target: int) -> int:
    """Return index of nearest timestamp in sorted array."""
    pos = np.searchsorted(sorted_ts, target)
    if pos == 0:
        return 0
    if pos == len(sorted_ts):
        return len(sorted_ts) - 1
    before = sorted_ts[pos - 1]
    after = sorted_ts[pos]
    return pos - 1 if target - before <= after - target else pos

def linear_interp(t0, t1, v0, v1, t):
    if t1 == t0:
        return v0
    alpha = (t - t0) / (t1 - t0)
    return (1 - alpha) * v0 + alpha * v1

def sorted_items(d: Dict[int, np.ndarray]) -> Tuple[np.ndarray, np.ndarray]:
    """Dict[timestamp_ns] -> value; return (ts_array, values_stacked) if shapes align."""
    if not d:
        return np.array([], dtype=np.int64), None
    ts = np.array(sorted(d.keys()), dtype=np.int64)
    values = [d[t] for t in ts]
    try:
        arr = np.stack(values, axis=0)
    except Exception:
        # ragged -> keep list
        arr = values
    return ts, arr

# -----------------------------
# Bag readers
# -----------------------------

class RosbagsReader:
    """Read rosbag2 using rosbags library (db3/mcap)."""
    def __init__(self, bag_path: str):
        from rosbags.highlevel import AnyReader
        self._AnyReader = AnyReader
        self.reader = AnyReader([bag_path])
        self.reader.open()
        self.connections = {c.id: c for c in self.reader.connections}

    def messages(self):
        for conn, stamp, raw in self.reader.messages():
            topic = self.connections[conn].topic
            yield topic, int(stamp), raw, self.connections[conn].msgtype

    def decode(self, raw, msgtype):
        return self.reader.deserialize(raw, msgtype)

    def close(self):
        self.reader.close()

class Rosbag2PyReader:
    """Read rosbag2 using rosbag2_py (requires sourced ROS2)."""
    def __init__(self, bag_path: str):
        import rosbag2_py
        self.reader = rosbag2_py.SequentialReader()
        storage_options = rosbag2_py.StorageOptions(uri=bag_path, storage_id="")
        converter_options = rosbag2_py.ConverterOptions(
            input_serialization_format="cdr",
            output_serialization_format="cdr",
        )
        self.reader.open(storage_options, converter_options)

        # build topic info
        topics = self.reader.get_all_topics_and_types()
        self.topic_types = {t.name: t.type for t in topics}

        # rosidl_runtime_py for decoding
        from rosidl_runtime_py.utilities import get_message
        self.get_message = get_message

    def messages(self):
        import rosbag2_py
        while self.reader.has_next():
            (topic, data, t) = self.reader.read_next()
            yield topic, int(t), data, self.topic_types.get(topic, "")

    def decode(self, data, msgtype):
        from rclpy.serialization import deserialize_message
        msg_cls = self.get_message(msgtype)
        return deserialize_message(data, msg_cls)

    def close(self):
        pass

# -----------------------------
# Decoders for messages
# -----------------------------

def decode_image(msg) -> Image.Image:
    """sensor_msgs/msg/Image -> PIL.Image (RGB)"""
    # msg.encoding could be 'rgb8', 'bgr8', 'mono8', etc.
    # 我们只处理常见 'rgb8'/'bgr8'/'rgba8'/'bgra8'/'mono8'
    import numpy as np
    height = msg.height
    width = msg.width
    data = np.frombuffer(msg.data, dtype=np.uint8)

    if msg.encoding in ('rgb8', 'rgb16'):
        img = data.reshape((height, width, -1))
        if img.shape[2] == 3:
            return Image.fromarray(img, mode='RGB')
    elif msg.encoding in ('bgr8',):
        img = data.reshape((height, width, -1))
        if img.shape[2] == 3:
            # BGR -> RGB
            img = img[..., ::-1]
            return Image.fromarray(img, mode='RGB')
    elif msg.encoding in ('rgba8', 'bgra8'):
        img = data.reshape((height, width, -1))
        if img.shape[2] == 4:
            if msg.encoding == 'bgra8':
                img = img[..., [2,1,0,3]]  # BGRA -> RGBA
            return Image.fromarray(img, mode='RGBA').convert('RGB')
    elif msg.encoding in ('mono8',):
        img = data.reshape((height, width))
        return Image.fromarray(img, mode='L').convert('RGB')

    # fallback（有些相机驱动可能写入更生僻编码）
    try:
        from cv_bridge import CvBridge
        bridge = CvBridge()
        cv_img = bridge.imgmsg_to_cv2(msg, desired_encoding='rgb8')
        return Image.fromarray(cv_img[:, :, ::-1])  # cv2 BGR->RGB
    except Exception:
        raise RuntimeError(f"Unsupported image encoding: {msg.encoding}")

def decode_joint_states(msg, joint_order: List[str]) -> np.ndarray:
    """sensor_msgs/msg/JointState -> qpos[14] (float32)按照固定顺序"""
    name_to_pos = dict(zip(msg.name, msg.position))
    q = []
    for j in joint_order:
        if j not in name_to_pos:
            raise KeyError(f"Joint '{j}' not found in JointState message; have: {list(name_to_pos.keys())[:6]} ...")
        q.append(float(name_to_pos[j]))
    return np.array(q, dtype=np.float32)

def decode_f64_array(msg, expected_len: Optional[int] = None) -> np.ndarray:
    """std_msgs/msg/Float64MultiArray -> np.ndarray"""
    arr = np.array(msg.data, dtype=np.float32)
    if expected_len is not None and arr.shape[0] != expected_len:
        # 允许长度不符但给出警告
        # （长度错误时，后续会在拼接时抛出异常）
        pass
    return arr

# -----------------------------
# Main converter
# -----------------------------

class BagToLeRobotConverter:
    def __init__(self, topics: Topics, backend: str = "rosbags", image_resize: Optional[Tuple[int,int]] = None,
                 interpolate_actions: bool = False, fps: int = 30, robot_type: str = "dual_arm_robot",
                 sync_tolerance_ms: float = 100.0):
        self.topics = topics
        self.backend = backend
        self.image_resize = image_resize
        self.interpolate_actions = interpolate_actions
        self.fps = fps
        self.robot_type = robot_type
        self.sync_tolerance_ns = int(sync_tolerance_ms * 1e6)  # Convert ms to nanoseconds

    def _open_reader(self, bag_path: str):
        if self.backend == "rosbags":
            return RosbagsReader(bag_path)
        elif self.backend == "ros2py":
            return Rosbag2PyReader(bag_path)
        else:
            raise ValueError(f"Unknown backend: {self.backend}")

    def convert_many(self, input_dir: str, output_dir: str, repo_id: str):
        """Convert multiple bag files to LeRobotDataset format."""
        bag_paths = self._discover_bags(input_dir)
        if not bag_paths:
            raise FileNotFoundError(f"No rosbag2 found under {input_dir}")

        # Determine image shape from first bag
        image_shape = self._get_image_shape(bag_paths[0])
        features = get_lerobot_features(image_shape)

        # Create LeRobotDataset
        dataset = LeRobotDataset.create(
            repo_id=repo_id,
            fps=self.fps,
            features=features,
            root=output_dir,
            robot_type=self.robot_type,
            use_videos=True,
        )

        episode_index = 0
        for bag_path in bag_paths:
            print(f"Converting bag: {bag_path}")
            self._convert_single_bag(bag_path, dataset, episode_index)
            episode_index += 1

        return dataset

    def _discover_bags(self, input_dir: str) -> List[str]:
        """Discover bag directories containing .db3 or .mcap files."""
        cands = []
        for p in sorted(glob.glob(os.path.join(input_dir, "*"))):
            if os.path.isdir(p) and (glob.glob(os.path.join(p, "*.db3")) or glob.glob(os.path.join(p, "*.mcap"))):
                cands.append(p)
        return cands

    def _get_image_shape(self, bag_path: str) -> Tuple[int, int, int]:
        """Get image shape from first image in bag."""
        reader = self._open_reader(bag_path)
        try:
            for topic, t_ns, raw, msgtype in reader.messages():
                if topic == self.topics.cam_head:
                    try:
                        msg = reader.decode(raw, msgtype)
                        img = decode_image(msg)
                        if self.image_resize:
                            img = img.resize(self.image_resize, Image.BILINEAR)
                        return (img.height, img.width, 3)  # RGB
                    except:
                        continue
        finally:
            reader.close()
        # Default shape if no image found
        return (480, 640, 3)

    def _convert_single_bag(self, bag_path: str, dataset: LeRobotDataset, episode_index: int):
        """Convert a single bag file to LeRobotDataset episode."""
        reader = self._open_reader(bag_path)

        # Collect messages by topic and timestamp
        topic_messages = {
            self.topics.cam_head: {},
            self.topics.cam_left: {},
            self.topics.cam_right: {},
            self.topics.joint_states: {},
            self.topics.gripper_states: {},
            self.topics.left_action: {},
            self.topics.right_action: {},
            self.topics.gripper_action: {}
        }

        print(f"Reading messages from {bag_path}...")
        for topic, t_ns, raw, msgtype in tqdm(reader.messages(), desc="Reading messages"):
            if topic not in topic_messages:
                continue

            try:
                msg = reader.decode(raw, msgtype)
            except Exception:
                msg = raw  # for rosbags AnyReader, raw is already decoded

            topic_messages[topic][t_ns] = msg

        reader.close()

        # Use joint_states as the primary timeline for synchronization
        joint_timestamps = sorted(topic_messages[self.topics.joint_states].keys())
        if not joint_timestamps:
            print(f"No joint_states messages found in {bag_path}")
            return

        print(f"Found {len(joint_timestamps)} joint_states messages")
        print(f"Processing frames with temporal alignment...")

        # Initialize episode buffer
        dataset.episode_buffer = dataset.create_episode_buffer(episode_index)

        frame_count = 0

        for joint_ts in tqdm(joint_timestamps, desc="Processing frames"):
            try:
                frame = self._create_aligned_frame(topic_messages, joint_ts, self.sync_tolerance_ns)
                if frame is not None:
                    timestamp_s = joint_ts / 1e9  # Convert to seconds
                    dataset.add_frame(frame, task="manipulation", timestamp=timestamp_s)
                    frame_count += 1
            except Exception as e:
                print(f"Error processing frame at {joint_ts}: {e}")
                continue

        if frame_count > 0:
            print(f"Saving episode {episode_index} with {frame_count} frames...")
            dataset.save_episode()
        else:
            print(f"No valid frames found in {bag_path}")

    def _create_aligned_frame(self, topic_messages: Dict[str, Dict[int, any]],
                             target_timestamp: int, tolerance_ns: int) -> Optional[Dict[str, np.ndarray]]:
        """Create a frame by aligning messages from different topics to target timestamp."""
        frame = {}

        # Get joint states (required - exact timestamp)
        if target_timestamp not in topic_messages[self.topics.joint_states]:
            return None

        joint_msg = topic_messages[self.topics.joint_states][target_timestamp]
        try:
            qpos = decode_joint_states(joint_msg, JOINT_ORDER)
            frame["observation.state"] = qpos
        except Exception as e:
            print(f"Error decoding joint states: {e}")
            return None

        # Get gripper states (nearest neighbor within tolerance)
        gripper_msg = self._find_nearest_message(
            topic_messages[self.topics.gripper_states], target_timestamp, tolerance_ns
        )
        if gripper_msg is not None:
            try:
                frame["observation.gripper_state"] = decode_f64_array(gripper_msg, expected_len=2)
            except:
                frame["observation.gripper_state"] = np.zeros(2, dtype=np.float32)
        else:
            frame["observation.gripper_state"] = np.zeros(2, dtype=np.float32)

        # Get images (nearest neighbor within tolerance)
        image_topics = [
            (self.topics.cam_head, "observation.images.head"),
            (self.topics.cam_left, "observation.images.left_hand"),
            (self.topics.cam_right, "observation.images.right_hand")
        ]

        for topic, frame_key in image_topics:
            img_msg = self._find_nearest_message(
                topic_messages[topic], target_timestamp, tolerance_ns
            )
            if img_msg is not None:
                try:
                    img = decode_image(img_msg)
                    img = self._process_image(img)
                    frame[frame_key] = np.array(img)
                except Exception as e:
                    print(f"Error decoding image from {topic}: {e}")
                    frame[frame_key] = self._create_dummy_image()
            else:
                print(f"No image found for {topic} within tolerance")
                frame[frame_key] = self._create_dummy_image()

        # Get actions (nearest neighbor within tolerance)
        left_action_msg = self._find_nearest_message(
            topic_messages[self.topics.left_action], target_timestamp, tolerance_ns
        )
        right_action_msg = self._find_nearest_message(
            topic_messages[self.topics.right_action], target_timestamp, tolerance_ns
        )
        gripper_action_msg = self._find_nearest_message(
            topic_messages[self.topics.gripper_action], target_timestamp, tolerance_ns
        )

        left_action = np.zeros(7, dtype=np.float32)
        right_action = np.zeros(7, dtype=np.float32)
        gripper_action = np.zeros(2, dtype=np.float32)

        if left_action_msg is not None:
            try:
                left_action = decode_f64_array(left_action_msg, expected_len=7)
            except:
                pass

        if right_action_msg is not None:
            try:
                right_action = decode_f64_array(right_action_msg, expected_len=7)
            except:
                pass

        if gripper_action_msg is not None:
            try:
                gripper_action = decode_f64_array(gripper_action_msg, expected_len=2)
            except:
                pass

        # Combine actions into single vector
        frame["action"] = np.concatenate([left_action, right_action, gripper_action], axis=0)

        return frame

    def _find_nearest_message(self, messages: Dict[int, any], target_timestamp: int,
                             tolerance_ns: int) -> Optional[any]:
        """Find the message with timestamp closest to target within tolerance."""
        if not messages:
            return None

        timestamps = np.array(list(messages.keys()))
        diffs = np.abs(timestamps - target_timestamp)
        min_idx = np.argmin(diffs)

        if diffs[min_idx] <= tolerance_ns:
            return messages[timestamps[min_idx]]
        else:
            return None

    def _process_image(self, img: Image.Image) -> Image.Image:
        """Process image with proper resizing and padding to maintain aspect ratio."""
        if self.image_resize is None:
            return img

        target_width, target_height = self.image_resize
        original_width, original_height = img.size

        # Calculate scaling factor to fit image within target size while maintaining aspect ratio
        scale_w = target_width / original_width
        scale_h = target_height / original_height
        scale = min(scale_w, scale_h)

        # Calculate new size after scaling
        new_width = int(original_width * scale)
        new_height = int(original_height * scale)

        # Resize image
        img_resized = img.resize((new_width, new_height), Image.BILINEAR)

        # Create new image with target size and paste resized image in center
        img_padded = Image.new('RGB', (target_width, target_height), (0, 0, 0))

        # Calculate padding offsets to center the image
        offset_x = (target_width - new_width) // 2
        offset_y = (target_height - new_height) // 2

        img_padded.paste(img_resized, (offset_x, offset_y))

        return img_padded

    def _create_dummy_image(self) -> np.ndarray:
        """Create a dummy black image with the target dimensions."""
        if self.image_resize:
            width, height = self.image_resize
            return np.zeros((height, width, 3), dtype=np.uint8)
        else:
            return np.zeros((480, 640, 3), dtype=np.uint8)



# -----------------------------
# CLI
# -----------------------------

def parse_args():
    p = argparse.ArgumentParser(description="Convert ROS2 bag files to LeRobotDataset format")
    p.add_argument("--input", required=True, help="Directory containing multiple rosbag2 folders.")
    p.add_argument("--output", required=True, help="Output LeRobotDataset directory.")
    p.add_argument("--repo-id", required=True, help="Repository ID for the dataset.")
    p.add_argument("--fps", type=int, default=30, help="Frames per second for the dataset.")
    p.add_argument("--robot-type", default="dual_arm_robot", help="Robot type identifier.")
    p.add_argument("--camera-head", default="/camera/color/image_raw")
    p.add_argument("--camera-left", default="/camera1/camera1/color/image_raw")
    p.add_argument("--camera-right", default="/camera2/camera2/color/image_raw")
    p.add_argument("--joint-states", default="/joint_states")
    p.add_argument("--gripper-states", default="/gripper_states")
    p.add_argument("--left-action", default="/left_arm_position_controller/commands")
    p.add_argument("--right-action", default="/right_arm_position_controller/commands")
    p.add_argument("--gripper-action", default="/gripper_commands")
    p.add_argument("--backend", choices=["rosbags","ros2py"], default="rosbags")
    p.add_argument("--resize", type=str, default=None, help="e.g., 640x480")
    p.add_argument("--interp-actions", action="store_true", help="Enable linear interpolation for actions.")
    p.add_argument("--sync-tolerance", type=float, default=100.0,
                   help="Synchronization tolerance in milliseconds (default: 100ms)")
    return p.parse_args()

def main():
    args = parse_args()
    resize = None
    if args.resize:
        w, h = args.resize.lower().split("x")
        resize = (int(w), int(h))

    topics = Topics(
        cam_head=args.camera_head,
        cam_left=args.camera_left,
        cam_right=args.camera_right,
        joint_states=args.joint_states,
        gripper_states=args.gripper_states,
        left_action=args.left_action,
        right_action=args.right_action,
        gripper_action=args.gripper_action,
    )

    conv = BagToLeRobotConverter(
        topics=topics,
        backend=args.backend,
        image_resize=resize,
        interpolate_actions=args.interp_actions,
        fps=args.fps,
        robot_type=args.robot_type,
        sync_tolerance_ms=args.sync_tolerance,
    )

    dataset = conv.convert_many(args.input, args.output, args.repo_id)
    print(f"Done. Saved LeRobotDataset to: {args.output}")
    print(f"Dataset info: {dataset}")

if __name__ == "__main__":
    main()
