#!/usr/bin/env python3
"""
Simple test script for YMrobot integration with LeRobot framework.

This script tests core functionality without problematic imports.

Usage:
    python test_ymrobot_simple.py
"""

import sys
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from lerobot.robots.ymrobot import YMrobotConfig
from lerobot.cameras import CameraConfig


def test_ymrobot_config():
    """Test YMrobot configuration creation."""
    print("Testing YMrobot configuration...")
    
    # Test basic configuration
    config = YMrobotConfig(
        id="test_ymrobot",
        cameras={
            "head_camera": CameraConfig(width=640, height=480, fps=30),
            "left_wrist_camera": CameraConfig(width=640, height=480, fps=30),
            "right_wrist_camera": CameraConfig(width=640, height=480, fps=30),
        }
    )
    
    assert config.type == "ymrobot"
    assert config.id == "test_ymrobot"
    assert len(config.cameras) == 3
    assert config.control_frequency == 50.0
    assert len(config.left_arm_joint_names) == 7
    assert len(config.right_arm_joint_names) == 7
    
    print("✓ YMrobot configuration test passed")
    return config


def test_robot_registration():
    """Test that YMrobot is properly registered in LeRobot framework."""
    print("Testing robot registration...")
    
    try:
        from lerobot import available_robots
        
        # Check if ymrobot is in available robots list
        assert "ymrobot" in available_robots, "YMrobot not found in available_robots list"
        
        # Test configuration registration
        config = YMrobotConfig(id="test_registration")
        assert config.type == "ymrobot"
        
        print("✓ Robot registration test passed")
        return True
        
    except Exception as e:
        print(f"✗ Robot registration test failed: {e}")
        return False


def test_robot_class_attributes():
    """Test robot class attributes and structure."""
    print("Testing robot class attributes...")
    
    try:
        from lerobot.robots.ymrobot import YMrobot
        
        # Test robot class attributes
        assert YMrobot.name == "ymrobot"
        assert YMrobot.config_class == YMrobotConfig
        
        print("✓ Robot class attributes test passed")
        return True
        
    except Exception as e:
        print(f"✗ Robot class attributes test failed: {e}")
        return False


def test_make_robot_function():
    """Test make_robot_from_config function."""
    print("Testing make_robot_from_config function...")
    
    try:
        from lerobot.robots.utils import make_robot_from_config
        
        config = YMrobotConfig(id="test_make_robot")
        
        # This should work without actually creating the robot
        # since we're testing the registration mechanism
        try:
            robot = make_robot_from_config(config)
            # If we get here, the robot was created successfully
            # But it will fail to initialize due to missing ROS2
            print("✓ make_robot_from_config function works (robot creation will fail without ROS2)")
            return True
        except ImportError as e:
            if "ROS2 is not available" in str(e):
                print("✓ make_robot_from_config function works (correctly detects missing ROS2)")
                return True
            else:
                raise
        
    except Exception as e:
        print(f"✗ make_robot_from_config test failed: {e}")
        return False


def test_action_observation_features():
    """Test action and observation features structure."""
    print("Testing action and observation features...")
    
    try:
        from lerobot.robots.ymrobot import YMrobot
        
        # Create a dummy robot instance to test features
        # We can't actually instantiate it without ROS2, but we can test the class methods
        config = YMrobotConfig(id="test_features")
        
        # Test that the class has the required properties
        assert hasattr(YMrobot, 'observation_features')
        assert hasattr(YMrobot, 'action_features')
        
        print("✓ Action and observation features structure test passed")
        return True
        
    except Exception as e:
        print(f"✗ Action and observation features test failed: {e}")
        return False


def main():
    """Run all simple tests."""
    print("=" * 60)
    print("YMrobot Simple Integration Test Suite")
    print("=" * 60)
    
    tests = [
        test_ymrobot_config,
        test_robot_registration,
        test_robot_class_attributes,
        test_make_robot_function,
        test_action_observation_features,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            result = test_func()
            if result is not False:
                passed += 1
        except Exception as e:
            print(f"✗ {test_func.__name__} failed with exception: {e}")
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All simple tests passed! YMrobot integration successful.")
        print("\nYMrobot has been successfully integrated into LeRobot framework!")
        print("\nNext steps:")
        print("1. Start your YMrobot ROS2 system")
        print("2. Test with actual robot hardware using:")
        print("   lerobot-record --robot.type=ymrobot --robot.id=my_ymrobot ...")
        print("   lerobot-replay --robot.type=ymrobot --robot.id=my_ymrobot ...")
        print("   lerobot-teleoperate --robot.type=ymrobot --robot.id=my_ymrobot ...")
    else:
        print("✗ Some tests failed. Please check the errors above.")
    
    print("=" * 60)
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
