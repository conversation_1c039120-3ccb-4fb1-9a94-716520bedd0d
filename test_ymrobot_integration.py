#!/usr/bin/env python3
"""
Test script for YMrobot integration with LeRobot framework.

This script tests:
1. YMrobot configuration creation
2. Robot instantiation through LeRobot framework
3. Basic robot interface functionality
4. Camera configuration
5. Action and observation features

Usage:
    python test_ymrobot_integration.py
"""

import sys
import time
import numpy as np
import rclpy
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from lerobot.robots.utils import make_robot_from_config
from lerobot.robots.ymrobot import YMrobotConfig
from lerobot.cameras import CameraConfig


def test_ymrobot_config():
    """Test YMrobot configuration creation."""
    print("Testing YMrobot configuration...")
    
    # Test basic configuration
    config = YMrobotConfig(
        id="test_ymrobot",
        cameras={
            "head_camera": CameraConfig(width=640, height=480, fps=30),
            "left_wrist_camera": CameraConfig(width=640, height=480, fps=30),
            "right_wrist_camera": CameraConfig(width=640, height=480, fps=30),
        }
    )
    
    assert config.type == "ymrobot"
    assert config.id == "test_ymrobot"
    assert len(config.cameras) == 3
    assert config.control_frequency == 50.0
    assert len(config.left_arm_joint_names) == 7
    assert len(config.right_arm_joint_names) == 7
    
    print("✓ YMrobot configuration test passed")
    return config


def test_robot_instantiation(config):
    """Test robot instantiation through LeRobot framework."""
    print("Testing robot instantiation...")
    
    # Initialize ROS2 if not already initialized
    if not rclpy.ok():
        rclpy.init()
    
    try:
        # Create robot through LeRobot framework
        robot = make_robot_from_config(config)
        
        assert robot.name == "ymrobot"
        assert robot.config_class == YMrobotConfig
        assert robot.id == "test_ymrobot"
        
        print("✓ Robot instantiation test passed")
        return robot
        
    except Exception as e:
        print(f"✗ Robot instantiation failed: {e}")
        return None


def test_robot_features(robot):
    """Test robot observation and action features."""
    print("Testing robot features...")
    
    try:
        # Test observation features
        obs_features = robot.observation_features
        print(f"Observation features: {list(obs_features.keys())}")
        
        # Check motor features
        expected_motors = (
            [f"{joint}.pos" for joint in robot.config.left_arm_joint_names] +
            [f"{joint}.pos" for joint in robot.config.right_arm_joint_names] +
            ["left_gripper.pos", "right_gripper.pos"]
        )
        
        for motor in expected_motors:
            assert motor in obs_features, f"Missing motor feature: {motor}"
            assert obs_features[motor] == float, f"Wrong type for {motor}"
        
        # Check camera features
        for cam_name in robot.config.cameras:
            assert cam_name in obs_features, f"Missing camera feature: {cam_name}"
            assert obs_features[cam_name] == (480, 640, 3), f"Wrong shape for {cam_name}"
        
        # Test action features
        action_features = robot.action_features
        print(f"Action features: {list(action_features.keys())}")
        
        # Action features should match motor features
        for motor in expected_motors:
            assert motor in action_features, f"Missing action feature: {motor}"
            assert action_features[motor] == float, f"Wrong type for action {motor}"
        
        print("✓ Robot features test passed")
        return True
        
    except Exception as e:
        print(f"✗ Robot features test failed: {e}")
        return False


def test_robot_connection(robot):
    """Test robot connection (without actual hardware)."""
    print("Testing robot connection...")
    
    try:
        # Test connection status
        assert not robot.is_connected, "Robot should not be connected initially"
        assert robot.is_calibrated, "YMrobot should always be calibrated"
        
        # Note: We can't actually connect without ROS2 topics running
        # This would require the actual YMrobot ROS2 system to be running
        print("✓ Robot connection interface test passed")
        return True
        
    except Exception as e:
        print(f"✗ Robot connection test failed: {e}")
        return False


def test_action_format(robot):
    """Test action format and structure."""
    print("Testing action format...")
    
    try:
        # Create a sample action
        action = {}
        
        # Add left arm joint actions
        for i, joint_name in enumerate(robot.config.left_arm_joint_names):
            action[f"{joint_name}.pos"] = float(i * 0.1)
        
        # Add right arm joint actions
        for i, joint_name in enumerate(robot.config.right_arm_joint_names):
            action[f"{joint_name}.pos"] = float(i * 0.1)
        
        # Add gripper actions
        action["left_gripper.pos"] = 0.5
        action["right_gripper.pos"] = 0.7
        
        # Verify action structure matches action features
        action_features = robot.action_features
        assert len(action) == len(action_features), "Action length mismatch"
        
        for key in action:
            assert key in action_features, f"Unexpected action key: {key}"
            assert isinstance(action[key], (int, float)), f"Wrong action type for {key}"
        
        print(f"✓ Action format test passed (action has {len(action)} elements)")
        return True
        
    except Exception as e:
        print(f"✗ Action format test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("YMrobot Integration Test Suite")
    print("=" * 60)
    
    try:
        # Test 1: Configuration
        config = test_ymrobot_config()
        if config is None:
            return False
        
        # Test 2: Robot instantiation
        robot = test_robot_instantiation(config)
        if robot is None:
            return False
        
        # Test 3: Robot features
        if not test_robot_features(robot):
            return False
        
        # Test 4: Robot connection interface
        if not test_robot_connection(robot):
            return False
        
        # Test 5: Action format
        if not test_action_format(robot):
            return False
        
        print("=" * 60)
        print("✓ All tests passed! YMrobot integration successful.")
        print("=" * 60)
        
        # Cleanup
        try:
            robot.disconnect()
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"✗ Test suite failed with error: {e}")
        return False
    
    finally:
        # Cleanup ROS2
        try:
            if rclpy.ok():
                rclpy.shutdown()
        except:
            pass


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
